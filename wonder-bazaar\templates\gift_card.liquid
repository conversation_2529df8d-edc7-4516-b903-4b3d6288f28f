{% layout none %}

<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    {% # Inlined CSS Variables %}
    {% render 'css-variables' %}

    {% # Load and preload the critical CSS %}
    {{ 'critical.css' | asset_url | stylesheet_tag: preload: true }}

    {% # Social, title, etc. %}
    {% render 'meta-tags' %}

    {% style %}
      main {
        text-align: center;
      }
      main img {
        display: unset;
      }
    {% endstyle %}

    {{ content_for_header }}
  </head>

  <body>
    <main>
      <div class="shopify-section">
        <h1>{{ gift_card.balance | money }}</h1>

        {% if gift_card.enabled == false or gift_card.expired %}
          <p>{{ 'gift_card.expired' | t }}</p>
        {% endif %}

        {% if gift_card.expires_on %}
          {% assign expires_on = gift_card.expires_on | date: '%B %e, %Y' %}
          <p>
            {{ 'gift_card.expires_on' | t: expires_on: expires_on }}
          </p>
        {% endif %}

        <p>
          {% if settings.logo %}
            {{ settings.logo | image_url: width: 300 | image_tag: alt: shop.name }}
          {% else %}
            <img
              src="{{ 'gift-card/card.svg' | shopify_asset_url }}"
              alt="{{ 'gift_card.card' | t }}"
              width="300"
              height="200"
            >
          {% endif %}
        </p>

        <h2>{{ shop.name }}</h2>
        <p>{{ 'gift_card.use_at_checkout' | t }}</p>
        <p>{{ gift_card.code | format_code }}</p>

        {% if gift_card.pass_url %}
          <a href="{{ gift_card.pass_url }}">
            <img
              src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}"
              alt="{{ 'gift_card.add_to_apple_wallet' | t }}"
              width="200"
              height="60"
            >
          </a>
        {% endif %}
      </div>
    </main>
  </body>
</html>
