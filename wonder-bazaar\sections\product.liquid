{% comment %}
  This section is used in the product template to render product page with
  media, content, and add-to-cart form.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/product
{% endcomment %}

<div class="product-images">
  {% for image in product.images %}
    {% render 'image', class: 'product-image', image: image %}
  {% endfor %}
</div>

<div class="product-info">
  <h1>{{ product.title }}</h1>
  <p>{{ product.price | money }}</p>
  <p>{{ product.description }}</p>
</div>

<div class="product-form">
  {% form 'product', product %}
    {% assign current_variant = product.selected_or_first_available_variant %}

    <select name="id">
      {% for variant in product.variants %}
        <option
          value="{{ variant.id }}"
          {% if variant == current_variant %}
            selected="selected"
          {% endif %}
        >
          {{ variant.title }} - {{ variant.price | money }}
        </option>
      {% endfor %}
    </select>

    <input type="text" name="quantity" min="1" value="1">

    <input type="submit" value="Add to cart">
    {{ form | payment_button }}
  {% endform %}
</div>

{% schema %}
{
  "name": "t:general.product",
  "settings": [],
  "disabled_on": {
    "groups": ["header", "footer"]
  }
}
{% endschema %}
