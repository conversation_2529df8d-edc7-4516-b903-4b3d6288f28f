{% comment %}
  Featured Products Section
  Displays a carousel of featured products with navigation and swipe functionality
{% endcomment %}

{% liquid
  # Determine which products to show based on settings
  case section.settings.product_source
    when 'collection'
      if section.settings.collection != blank
        assign featured_products = collections[section.settings.collection].products
      else
        assign featured_products = collections.all.products
      endif
    when 'recent'
      assign featured_products = collections.all.products | sort: 'created_at' | reverse
    when 'best_selling'
      # For best selling, we'll use products sorted by availability and price as a proxy
      # In a real implementation, you'd want to track sales data via metafields or apps
      assign featured_products = collections.all.products | where: 'available', true | sort: 'price' | reverse
    when 'featured'
      # Show products that have been marked as featured (you can use tags or metafields)
      assign featured_products = collections.all.products | where: 'tags', 'featured'
      if featured_products.size == 0
        assign featured_products = collections.all.products | sort: 'created_at' | reverse
      endif
    when 'manual'
      assign featured_products = section.settings.products
    else
      # Default to recent products
      assign featured_products = collections.all.products | sort: 'created_at' | reverse
  endcase

  # Filter out unavailable products if setting is enabled
  if section.settings.hide_unavailable
    assign featured_products = featured_products | where: 'available', true
  endif

  # Limit to specified number of products
  assign products_limit = section.settings.products_count | default: 6
  assign featured_products = featured_products | slice: 0, products_limit
%}

<section class="featured-products full-width" id="featured-products-{{ section.id }}">
  <div class="featured-products__header">
    <h2 class="featured-products__title">{{ section.settings.heading | default: 'Featured Collection' }}</h2>
    {% if section.settings.show_view_all and section.settings.collection != blank %}
      <a href="{{ collections[section.settings.collection].url }}" class="featured-products__view-all">
        View All
      </a>
    {% endif %}
  </div>

  <div class="featured-products__container">
    <button class="featured-products__nav featured-products__nav--prev" aria-label="Previous product">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>

    <div class="featured-products__carousel">
      <div class="featured-products__track" id="products-track-{{ section.id }}">
        {% for product in featured_products %}
          <div class="product-card" data-product-id="{{ product.id }}">
            <div class="product-card__image-container">
              {% if product.featured_image %}
                {% render 'image',
                  class: 'product-card__image',
                  image: product.featured_image,
                  url: product.url,
                  width: 400,
                  height: 400,
                  crop: 'center'
                %}
              {% else %}
                <div class="product-card__image-placeholder">
                  <svg width="100" height="100" viewBox="0 0 100 100" fill="none">
                    <rect width="100" height="100" fill="#f3f4f6"/>
                    <path d="M30 40L50 20L70 40M50 20V80" stroke="#9ca3af" stroke-width="2"/>
                  </svg>
                </div>
              {% endif %}
            </div>

            <div class="product-card__content">
              <h3 class="product-card__title">
                <a href="{{ product.url }}">{{ product.title | escape }}</a>
              </h3>
              
              {% if product.description != blank %}
                <p class="product-card__description">
                  {{ product.description | strip_html | truncatewords: 10 }}
                </p>
              {% endif %}

              <div class="product-card__footer">
                <div class="product-card__pricing">
                  <span class="product-card__price">{{ product.price | money }}</span>
                  {% if product.compare_at_price > product.price %}
                    <span class="product-card__compare-price">{{ product.compare_at_price | money }}</span>
                  {% endif %}
                </div>

                <form action="/cart/add" method="post" enctype="multipart/form-data" class="product-card__form">
                  <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                  <button type="submit" class="product-card__add-to-cart" {% unless product.available %}disabled{% endunless %}>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </form>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>

    <button class="featured-products__nav featured-products__nav--next" aria-label="Next product">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
</section>

{% stylesheet %}
  .featured-products {
    padding: 2rem 1rem;
    background-color: var(--color-background);
  }

  .featured-products__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .featured-products__title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--color-foreground);
  }

  .featured-products__view-all {
    font-size: 0.9rem;
    color: var(--color-foreground);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .featured-products__view-all:hover {
    opacity: 1;
  }

  .featured-products__container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .featured-products__nav {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 2;
    flex-shrink: 0;
  }

  .featured-products__nav:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .featured-products__nav:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .featured-products__carousel {
    flex: 1;
    overflow: hidden;
    border-radius: 12px;
  }

  .featured-products__track {
    display: flex;
    transition: transform 0.3s ease;
    gap: 1rem;
  }

  .product-card {
    flex: 0 0 280px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    opacity: 1;
  }

  .product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .product-card.adjacent {
    opacity: 0.6;
    transform: scale(0.95);
  }

  .product-card__image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
  }

  .product-card__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .product-card__image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
  }

  .product-card__content {
    padding: 1rem;
  }

  .product-card__title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .product-card__title a {
    color: var(--color-foreground);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .product-card__title a:hover {
    color: #666;
  }

  .product-card__description {
    margin: 0 0 1rem 0;
    font-size: 0.875rem;
    color: #666;
    line-height: 1.4;
  }

  .product-card__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .product-card__pricing {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .product-card__price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-foreground);
  }

  .product-card__compare-price {
    font-size: 0.9rem;
    color: #999;
    text-decoration: line-through;
  }

  .product-card__form {
    margin: 0;
  }

  .product-card__add-to-cart {
    background: var(--color-foreground);
    color: var(--color-background);
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .product-card__add-to-cart:hover {
    transform: scale(1.05);
    opacity: 0.9;
  }

  .product-card__add-to-cart:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    .featured-products {
      padding: 1.5rem 0.5rem;
    }

    .featured-products__header {
      padding: 0 0.5rem;
      margin-bottom: 1rem;
    }

    .featured-products__title {
      font-size: 1.3rem;
    }

    .featured-products__nav {
      width: 40px;
      height: 40px;
    }

    .product-card {
      flex: 0 0 240px;
    }

    .product-card__image-container {
      height: 160px;
    }

    .product-card__content {
      padding: 0.75rem;
    }

    .product-card__title {
      font-size: 0.9rem;
    }

    .product-card__description {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .featured-products {
      padding: 1rem 0.25rem;
    }

    .featured-products__container {
      gap: 0.5rem;
    }

    .featured-products__nav {
      width: 36px;
      height: 36px;
    }

    .product-card {
      flex: 0 0 200px;
    }

    .product-card__image-container {
      height: 140px;
    }

    .product-card__add-to-cart {
      width: 36px;
      height: 36px;
    }
  }
{% endstylesheet %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const carousel = document.querySelector('#featured-products-{{ section.id }}');
    if (!carousel) return;

    const track = carousel.querySelector('.featured-products__track');
    const prevBtn = carousel.querySelector('.featured-products__nav--prev');
    const nextBtn = carousel.querySelector('.featured-products__nav--next');
    const cards = carousel.querySelectorAll('.product-card');

    if (!track || !prevBtn || !nextBtn || cards.length === 0) return;

    let currentIndex = 0;
    let cardWidth = 0;
    let visibleCards = 1;
    let isDragging = false;
    let startX = 0;
    let currentX = 0;
    let initialTransform = 0;

    function calculateDimensions() {
      const containerWidth = carousel.querySelector('.featured-products__carousel').offsetWidth;
      cardWidth = cards[0].offsetWidth + 16; // Include gap
      visibleCards = Math.floor(containerWidth / cardWidth);
      if (visibleCards === 0) visibleCards = 1;
    }

    function updateCarousel() {
      calculateDimensions();
      const maxIndex = Math.max(0, cards.length - visibleCards);
      currentIndex = Math.min(currentIndex, maxIndex);

      const translateX = -currentIndex * cardWidth;
      track.style.transform = `translateX(${translateX}px)`;

      // Update navigation buttons
      prevBtn.disabled = currentIndex === 0;
      nextBtn.disabled = currentIndex >= maxIndex;

      // Update card states for adjacent preview effect
      cards.forEach((card, index) => {
        card.classList.remove('adjacent');
        if (index < currentIndex || index >= currentIndex + visibleCards) {
          card.classList.add('adjacent');
        }
      });
    }

    function goToNext() {
      const maxIndex = Math.max(0, cards.length - visibleCards);
      if (currentIndex < maxIndex) {
        currentIndex++;
        updateCarousel();
      }
    }

    function goToPrev() {
      if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
      }
    }

    // Navigation button events
    nextBtn.addEventListener('click', goToNext);
    prevBtn.addEventListener('click', goToPrev);

    // Touch/swipe events
    function handleStart(e) {
      isDragging = true;
      startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
      currentX = startX;

      const transform = track.style.transform;
      initialTransform = transform ? parseInt(transform.match(/-?\d+/)[0]) : 0;

      track.style.transition = 'none';
    }

    function handleMove(e) {
      if (!isDragging) return;

      e.preventDefault();
      currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
      const deltaX = currentX - startX;
      const newTransform = initialTransform + deltaX;

      track.style.transform = `translateX(${newTransform}px)`;
    }

    function handleEnd() {
      if (!isDragging) return;

      isDragging = false;
      track.style.transition = 'transform 0.3s ease';

      const deltaX = currentX - startX;
      const threshold = cardWidth * 0.3;

      if (deltaX > threshold) {
        goToPrev();
      } else if (deltaX < -threshold) {
        goToNext();
      } else {
        updateCarousel();
      }
    }

    // Mouse events
    track.addEventListener('mousedown', handleStart);
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);

    // Touch events
    track.addEventListener('touchstart', handleStart, { passive: false });
    track.addEventListener('touchmove', handleMove, { passive: false });
    track.addEventListener('touchend', handleEnd);

    // Prevent default drag behavior on images
    track.addEventListener('dragstart', e => e.preventDefault());

    // Keyboard navigation
    carousel.addEventListener('keydown', function(e) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        goToPrev();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        goToNext();
      }
    });

    // Initialize and handle resize
    calculateDimensions();
    updateCarousel();

    window.addEventListener('resize', function() {
      calculateDimensions();
      updateCarousel();
    });

    // Auto-play functionality (optional)
    if (cards.length > visibleCards) {
      let autoPlayInterval;

      function startAutoPlay() {
        autoPlayInterval = setInterval(() => {
          if (currentIndex >= cards.length - visibleCards) {
            currentIndex = 0;
          } else {
            currentIndex++;
          }
          updateCarousel();
        }, 5000);
      }

      function stopAutoPlay() {
        clearInterval(autoPlayInterval);
      }

      // Start auto-play
      startAutoPlay();

      // Pause on hover/focus
      carousel.addEventListener('mouseenter', stopAutoPlay);
      carousel.addEventListener('mouseleave', startAutoPlay);
      carousel.addEventListener('focusin', stopAutoPlay);
      carousel.addEventListener('focusout', startAutoPlay);
    }
  });
</script>

{% schema %}
{
  "name": "Featured Products",
  "tag": "section",
  "class": "featured-products-section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Featured Collection"
    },
    {
      "type": "select",
      "id": "product_source",
      "label": "Product Source",
      "options": [
        {
          "value": "recent",
          "label": "Recent Products"
        },
        {
          "value": "best_selling",
          "label": "Best Selling"
        },
        {
          "value": "featured",
          "label": "Featured Products (tagged 'featured')"
        },
        {
          "value": "collection",
          "label": "Collection"
        },
        {
          "value": "manual",
          "label": "Manual Selection"
        }
      ],
      "default": "recent"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "product_list",
      "id": "products",
      "label": "Products",
      "limit": 12
    },
    {
      "type": "range",
      "id": "products_count",
      "label": "Number of Products",
      "min": 3,
      "max": 12,
      "step": 1,
      "default": 6
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "label": "Show View All Link",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_unavailable",
      "label": "Hide Unavailable Products",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Featured Products",
      "category": "Product"
    }
  ]
}
{% endschema %}
