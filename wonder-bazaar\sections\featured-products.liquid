{% comment %}
  Featured Products Section
  Displays a carousel of featured products with navigation and swipe functionality
{% endcomment %}

{% liquid
  # Smart product selection with fallback logic
  # Priority: Most Ordered → Recent → Custom Collection

  assign products_limit = section.settings.products_count | default: 6

  # Start with all available products as base
  assign all_products = collections.all.products

  if section.settings.product_source == 'manual' and section.settings.products.size > 0
    # Manual selection takes priority
    assign featured_products = section.settings.products
  else
    # Auto mode: try most ordered first (using available products sorted by price as proxy)
    assign featured_products = all_products | where: 'available', true | sort: 'price' | reverse

    # If no available products, fallback to recent products
    if featured_products.size == 0
      assign featured_products = all_products | sort: 'created_at' | reverse
    endif

    # If still no products, use all products
    if featured_products.size == 0
      assign featured_products = all_products
    endif
  endif

  # Filter out unavailable products if setting is enabled
  if section.settings.hide_unavailable
    assign featured_products = featured_products | where: 'available', true
  endif

  # Limit to specified number of products
  assign featured_products = featured_products | slice: 0, products_limit
%}

<section class="featured-products full-width" id="featured-products-{{ section.id }}">
  <div class="featured-products__container">
    <button class="featured-products__nav featured-products__nav--prev" aria-label="Previous product">
      <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>

    <div class="featured-products__stack-container">
      <div class="featured-products__stack" id="products-stack-{{ section.id }}">
        {% comment %} Create multiple copies for true infinite loop {% endcomment %}
        {% assign loop_count = 3 %}
        {% for i in (1..loop_count) %}
          {% for product in featured_products %}
            <div class="product-card-stack" data-product-id="{{ product.id }}" data-index="{{ forloop.index0 | plus: featured_products.size | times: forloop.parentloop.index0 }}" data-original-index="{{ forloop.index0 }}">
              <div class="product-card-stack__image-container">
                {% if product.featured_image %}
                  {% render 'image',
                    class: 'product-card-stack__image',
                    image: product.featured_image,
                    url: product.url,
                    width: 600,
                    height: 500,
                    crop: 'center'
                  %}
                {% else %}
                  <div class="product-card-stack__image-placeholder">
                    <svg width="120" height="120" viewBox="0 0 100 100" fill="none">
                      <rect width="100" height="100" fill="#f3f4f6"/>
                      <path d="M30 40L50 20L70 40M50 20V80" stroke="#9ca3af" stroke-width="2"/>
                    </svg>
                  </div>
                {% endif %}
              </div>

              <a href="{{ product.url }}" class="product-card-stack__link">
                <div class="product-card-stack__content">
                  <div class="product-card-stack__top-row">
                    <div class="product-card-stack__left">
                      <h3 class="product-card-stack__title">{{ product.title | escape }}</h3>
                    </div>
                    <div class="product-card-stack__right">
                      <div class="product-card-stack__pricing">
                        <span class="product-card-stack__price">₹{{ product.price | money_without_currency }}</span>
                        {% if product.compare_at_price > product.price %}
                          <span class="product-card-stack__compare-price">₹{{ product.compare_at_price | money_without_currency }}</span>
                        {% endif %}
                      </div>
                    </div>
                  </div>

                  {% if product.description != blank %}
                    <p class="product-card-stack__description">
                      {{ product.description | strip_html | truncatewords: 20 }}
                    </p>
                  {% endif %}

                  <div class="product-card-stack__bottom">
                    <form action="/cart/add" method="post" enctype="multipart/form-data" class="product-card-stack__form" onclick="event.stopPropagation(); event.preventDefault();">
                      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                      <button type="submit" class="product-card-stack__add-to-cart" {% unless product.available %}disabled{% endunless %}>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Add to Cart
                      </button>
                    </form>
                  </div>
                </div>
              </a>
            </div>
          {% endfor %}
        {% endfor %}
      </div>
    </div>

    <button class="featured-products__nav featured-products__nav--next" aria-label="Next product">
      <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>

  <div class="featured-products__footer">
    <div class="featured-products__arrows">
      <div class="featured-products__arrow-up">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="featured-products__arrow-up">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>
    <h2 class="featured-products__title" id="dynamic-title-{{ section.id }}">FEATURED COLLECTION</h2>
  </div>
</section>

{% stylesheet %}
  .featured-products {
    padding: 2rem 1rem;
    background-color: var(--color-background);
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .featured-products__container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    flex: 1;
    min-height: calc(100vh / 3);
  }

  .featured-products__nav {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    flex-shrink: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .featured-products__nav:hover {
    background: rgba(0, 0, 0, 1);
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  }

  .featured-products__nav:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
  }

  .featured-products__stack-container {
    flex: 1;
    position: relative;
    height: calc(100vh / 3);
    max-width: 900px;
    perspective: 1200px;
  }

  .featured-products__stack {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .product-card-stack {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 400px;
    height: calc(100vh / 3 - 40px);
    max-height: 500px;
    min-height: 350px;
    background: white;
    border-radius: 24px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center center;
    overflow: hidden;
    cursor: pointer;
  }

  .product-card-stack:hover {
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
  }

  /* Stacking positions */
  .product-card-stack[data-position="0"] {
    transform: translate(-50%, -50%) scale(1) rotateY(0deg) translateZ(0px);
    z-index: 5;
    opacity: 1;
  }

  .product-card-stack[data-position="1"] {
    transform: translate(-30%, -50%) scale(0.85) rotateY(-15deg) translateZ(-50px);
    z-index: 4;
    opacity: 0.8;
  }

  .product-card-stack[data-position="-1"] {
    transform: translate(-70%, -50%) scale(0.85) rotateY(15deg) translateZ(-50px);
    z-index: 4;
    opacity: 0.8;
  }

  .product-card-stack[data-position="2"] {
    transform: translate(-10%, -50%) scale(0.7) rotateY(-25deg) translateZ(-100px);
    z-index: 3;
    opacity: 0.6;
  }

  .product-card-stack[data-position="-2"] {
    transform: translate(-90%, -50%) scale(0.7) rotateY(25deg) translateZ(-100px);
    z-index: 3;
    opacity: 0.6;
  }

  .product-card-stack[data-position="hidden"] {
    transform: translate(-50%, -50%) scale(0.5) rotateY(45deg) translateZ(-200px);
    z-index: 1;
    opacity: 0;
  }

  .product-card-stack__link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: inherit;
  }

  .product-card-stack__image-container {
    position: relative;
    width: 100%;
    height: 55%;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .product-card-stack__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .product-card-stack:hover .product-card-stack__image {
    transform: scale(1.05);
  }

  .product-card-stack__image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  }

  .product-card-stack__content {
    padding: 1.5rem;
    height: 45%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .product-card-stack__top-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .product-card-stack__left {
    flex: 1;
    margin-right: 1rem;
  }

  .product-card-stack__right {
    flex-shrink: 0;
    text-align: right;
  }

  .product-card-stack__title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.3;
    font-family: var(--font-heading);
    color: var(--color-foreground);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .product-card-stack__pricing {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
  }

  .product-card-stack__price {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--color-foreground);
    font-family: var(--font-heading);
  }

  .product-card-stack__compare-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
    font-weight: 500;
  }

  .product-card-stack__description {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .product-card-stack__bottom {
    display: flex;
    justify-content: flex-end;
  }

  .product-card-stack__form {
    margin: 0;
  }

  .product-card-stack__add-to-cart {
    background: var(--color-foreground);
    color: var(--color-background);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-size: 0.9rem;
    font-weight: 600;
  }

  .product-card-stack__add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }

  .product-card-stack__add-to-cart:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .featured-products__footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 0;
    margin-top: 2rem;
  }

  .featured-products__arrows {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .featured-products__arrow-up {
    background: var(--color-foreground);
    color: var(--color-background);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 2s infinite;
  }

  .featured-products__arrow-up:nth-child(2) {
    animation-delay: 0.2s;
  }

  .featured-products__title {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: var(--color-foreground);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    text-align: center;
    transition: all 0.5s ease;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    .featured-products {
      padding: 2rem 0.5rem;
    }

    .featured-products__title {
      font-size: 2rem;
    }

    .featured-products__container {
      gap: 1rem;
      min-height: 400px;
    }

    .featured-products__nav {
      width: 50px;
      height: 50px;
    }

    .featured-products__stack-container {
      height: 350px;
      max-width: 600px;
    }

    .product-card-stack {
      width: 300px;
      height: 280px;
    }

    .product-card-stack__title {
      font-size: 1.2rem;
    }

    .product-card-stack__price {
      font-size: 1.5rem;
    }

    .product-card-stack__content {
      padding: 1rem;
    }
  }

  @media (max-width: 480px) {
    .featured-products {
      padding: 1.5rem 0.25rem;
    }

    .featured-products__title {
      font-size: 1.5rem;
    }

    .featured-products__container {
      gap: 0.5rem;
      min-height: 320px;
    }

    .featured-products__nav {
      width: 40px;
      height: 40px;
    }

    .featured-products__stack-container {
      height: 280px;
    }

    .product-card-stack {
      width: 250px;
      height: 220px;
    }

    .product-card-stack__title {
      font-size: 1rem;
    }

    .product-card-stack__price {
      font-size: 1.3rem;
    }

    .product-card-stack__add-to-cart {
      width: 40px;
      height: 40px;
    }
  }
{% endstylesheet %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const carousel = document.querySelector('#featured-products-{{ section.id }}');
    if (!carousel) return;

    const stack = carousel.querySelector('.featured-products__stack');
    const prevBtn = carousel.querySelector('.featured-products__nav--prev');
    const nextBtn = carousel.querySelector('.featured-products__nav--next');
    const cards = carousel.querySelectorAll('.product-card-stack');
    const titleElement = carousel.querySelector('#dynamic-title-{{ section.id }}');

    if (!stack || !prevBtn || !nextBtn || cards.length === 0) return;

    // Dynamic titles array
    const dynamicTitles = [
      'HOT PICKS',
      'TOP TRENDS',
      'NOW TRENDING',
      'SPOTLIGHT COLLECTION',
      'MUST-SEE PICKS',
      'CURRENT FAVES',
      'BUZZING NOW',
      'IN THE SPOTLIGHT',
      'TRENDING NOW',
      'POPULAR PICKS'
    ];

    let currentIndex = Math.floor(cards.length / 3); // Start in middle set
    let originalProductCount = {{ featured_products.size }};
    let isDragging = false;
    let startX = 0;
    let currentX = 0;
    let titleIndex = 0;

    function updateStack() {
      cards.forEach((card, index) => {
        const position = index - currentIndex;

        // Remove all position attributes first
        card.removeAttribute('data-position');

        // Set new position
        if (position === 0) {
          card.setAttribute('data-position', '0'); // Center/main card
        } else if (position === 1) {
          card.setAttribute('data-position', '1'); // Right side
        } else if (position === -1) {
          card.setAttribute('data-position', '-1'); // Left side
        } else if (position === 2) {
          card.setAttribute('data-position', '2'); // Far right
        } else if (position === -2) {
          card.setAttribute('data-position', '-2'); // Far left
        } else {
          card.setAttribute('data-position', 'hidden'); // Hidden
        }
      });

      // Check if we need to reset position for seamless loop
      if (currentIndex >= cards.length - originalProductCount) {
        // Moving towards end, reset to beginning set
        setTimeout(() => {
          currentIndex = originalProductCount;
          updateStackWithoutTransition();
        }, 600); // After transition completes
      } else if (currentIndex < originalProductCount) {
        // Moving towards beginning, reset to end set
        setTimeout(() => {
          currentIndex = cards.length - (originalProductCount * 2);
          updateStackWithoutTransition();
        }, 600);
      }

      // Navigation buttons are always enabled for infinite loop
      prevBtn.disabled = false;
      nextBtn.disabled = false;
    }

    function updateStackWithoutTransition() {
      // Temporarily disable transitions
      cards.forEach(card => {
        card.style.transition = 'none';
      });

      updateStackPositions();

      // Re-enable transitions after a frame
      requestAnimationFrame(() => {
        cards.forEach(card => {
          card.style.transition = '';
        });
      });
    }

    function updateStackPositions() {
      cards.forEach((card, index) => {
        const position = index - currentIndex;
        card.removeAttribute('data-position');

        if (position === 0) {
          card.setAttribute('data-position', '0');
        } else if (position === 1) {
          card.setAttribute('data-position', '1');
        } else if (position === -1) {
          card.setAttribute('data-position', '-1');
        } else if (position === 2) {
          card.setAttribute('data-position', '2');
        } else if (position === -2) {
          card.setAttribute('data-position', '-2');
        } else {
          card.setAttribute('data-position', 'hidden');
        }
      });
    }

    function goToNext() {
      currentIndex++;
      updateStack();
      rotateTitleText();
    }

    function goToPrev() {
      currentIndex--;
      updateStack();
      rotateTitleText();
    }

    function rotateTitleText() {
      if (titleElement) {
        titleIndex = (titleIndex + 1) % dynamicTitles.length;
        titleElement.style.opacity = '0';
        setTimeout(() => {
          titleElement.textContent = dynamicTitles[titleIndex];
          titleElement.style.opacity = '1';
        }, 250);
      }
    }

    function goToCard(index) {
      if (index >= 0 && index < cards.length) {
        currentIndex = index;
        updateStack();
      }
    }

    // Navigation button events
    nextBtn.addEventListener('click', goToNext);
    prevBtn.addEventListener('click', goToPrev);

    // Card click events - clicking on side cards brings them to center
    cards.forEach((card, index) => {
      card.addEventListener('click', function(e) {
        const position = card.getAttribute('data-position');
        if (position !== '0') {
          e.preventDefault();
          goToCard(index);
        }
      });

      // Add to cart functionality
      const addToCartForm = card.querySelector('.product-card-stack__form');
      if (addToCartForm) {
        addToCartForm.addEventListener('submit', function(e) {
          e.preventDefault();
          e.stopPropagation();

          const formData = new FormData(this);

          fetch('/cart/add.js', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            // Show success message or update cart
            console.log('Product added to cart:', data);
            // You can add a toast notification here
          })
          .catch(error => {
            console.error('Error adding to cart:', error);
          });
        });
      }
    });

    // Touch/swipe events
    function handleStart(e) {
      isDragging = true;
      startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
      currentX = startX;
    }

    function handleMove(e) {
      if (!isDragging) return;
      currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
    }

    function handleEnd() {
      if (!isDragging) return;

      isDragging = false;
      const deltaX = currentX - startX;
      const threshold = 50;

      if (deltaX > threshold) {
        goToPrev();
      } else if (deltaX < -threshold) {
        goToNext();
      }
    }

    // Mouse events
    stack.addEventListener('mousedown', handleStart);
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);

    // Touch events
    stack.addEventListener('touchstart', handleStart, { passive: true });
    stack.addEventListener('touchmove', handleMove, { passive: true });
    stack.addEventListener('touchend', handleEnd);

    // Keyboard navigation
    carousel.addEventListener('keydown', function(e) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        goToPrev();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        goToNext();
      }
    });

    // Initialize
    updateStack();

    // Start title rotation
    if (titleElement) {
      setInterval(() => {
        rotateTitleText();
      }, 3000);
    }

    // Auto-play functionality
    if (cards.length > 1) {
      let autoPlayInterval;

      function startAutoPlay() {
        autoPlayInterval = setInterval(() => {
          goToNext();
        }, 4000);
      }

      function stopAutoPlay() {
        clearInterval(autoPlayInterval);
      }

      // Start auto-play
      startAutoPlay();

      // Pause on hover/focus
      carousel.addEventListener('mouseenter', stopAutoPlay);
      carousel.addEventListener('mouseleave', startAutoPlay);
      carousel.addEventListener('focusin', stopAutoPlay);
      carousel.addEventListener('focusout', startAutoPlay);
    }
  });
</script>

{% schema %}
{
  "name": "Featured Products",
  "tag": "section",
  "class": "featured-products-section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "FEATURED COLLECTION"
    },
    {
      "type": "select",
      "id": "product_source",
      "label": "Product Source",
      "options": [
        {
          "value": "auto",
          "label": "Auto (Most Ordered → Recent → Collection)"
        },
        {
          "value": "manual",
          "label": "Manual Selection"
        }
      ],
      "default": "auto",
      "info": "Auto mode tries most ordered products first, then recent products, then falls back to collection products"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "product_list",
      "id": "products",
      "label": "Products",
      "limit": 24
    },
    {
      "type": "range",
      "id": "products_count",
      "label": "Number of Products",
      "min": 3,
      "max": 24,
      "step": 1,
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "hide_unavailable",
      "label": "Hide Unavailable Products",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Featured Products",
      "category": "Product"
    }
  ]
}
{% endschema %}
