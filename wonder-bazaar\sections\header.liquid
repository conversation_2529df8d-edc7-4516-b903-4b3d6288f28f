<div class="site-title full-width">
  <h1 class="site-title__text">
    <a href="{{ routes.root_url }}">WONDER BAZAAR</a>
  </h1>
</div>

{% stylesheet %}
  .site-title {
    background-color: var(--color-background);
    padding: 2rem 1rem;
    text-align: center;
    min-height: 6rem; /* Double the announcement bar height */
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 3px solid var(--color-foreground);
    margin-bottom: 2rem;
  }

  .site-title__text {
    margin: 0;
    font-family: 'Yatra One', cursive;
    font-size: 3rem;
    font-weight: bold;
    line-height: 1.2;
    letter-spacing: 0.05em;
    white-space: nowrap; /* Keep title on one line */
  }

  .site-title__text a {
    text-decoration: none;
    color: var(--color-foreground);
    transition: opacity 0.2s ease;
  }

  .site-title__text a:hover {
    opacity: 0.8;
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    .site-title {
      padding: 1.5rem 1rem;
      min-height: 5rem;
    }

    .site-title__text {
      font-size: 2.2rem; /* Slightly smaller for better fit */
    }
  }

  @media (max-width: 480px) {
    .site-title {
      padding: 1rem 0.75rem;
      min-height: 4rem; /* Reduced height for mobile */
    }

    .site-title__text {
      font-size: 1.6rem; /* Smaller size to fit on one line */
    }
  }

  @media (max-width: 360px) {
    .site-title {
      padding: 0.8rem 0.5rem;
      min-height: 3.5rem;
    }

    .site-title__text {
      font-size: 1.4rem; /* Even smaller for very small screens */
      letter-spacing: 0.03em; /* Reduce letter spacing for tighter fit */
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Site Title",
  "tag": "header",
  "class": "site-title-section",
  "settings": []
}
{% endschema %}
